'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTransition } from 'react'
import { toast } from 'sonner'
import { ArrowLeft, Save, Loader, Check } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertTriangle } from 'lucide-react'

import { SimpleManagerSelector } from '@/components/simple-manager-selector'
import { employeeFormSchema, type Employee as EmployeeSchema } from '@/lib/schemas'
import { saveEmployeeAction } from '@/lib/actions'
import { assignManagersToEmployeeAction } from '@/lib/actions/employees'
import type { Department, Manager } from '@/lib/types'

interface CurrentUser {
  id: string
  email: string
  fullName: string
  role: string
  imageUrl: string
}

interface AddEmployeeFormProps {
  departments: Department[]
  managers: Manager[]
  user: CurrentUser
}

type EmployeeFormData = Omit<EmployeeSchema, 'id'>

interface SelectedManager {
  id: string
  name: string
  isPrimary: boolean
}

export function AddEmployeeForm({ departments, managers, user }: AddEmployeeFormProps) {
  const router = useRouter()
  const [isPending, startTransition] = useTransition()
  const [submitError, setSubmitError] = React.useState<string | null>(null)
  const [isSuccess, setIsSuccess] = React.useState(false)
  const [selectedManagers, setSelectedManagers] = React.useState<SelectedManager[]>([])

  const form = useForm<EmployeeFormData>({
    resolver: zodResolver(employeeFormSchema),
    defaultValues: {
      fullName: "",
      email: "",
      role: "",
      linkedinUrl: "",
      twitterUrl: "",
      telegramUrl: "",
      departmentId: "",
      managerId: "none",
      active: true,
    },
  })

  const onSubmit = (data: EmployeeFormData) => {
    startTransition(async () => {
      try {
        setSubmitError(null)

        console.log('🔄 [ADD EMPLOYEE] Starting form submission...')
        console.log('🔄 [ADD EMPLOYEE] Form data:', data)
        console.log('🔄 [ADD EMPLOYEE] Selected managers:', selectedManagers)

        // Create FormData for the employee
        const formData = new FormData()
        formData.append('fullName', data.fullName)
        formData.append('email', data.email)
        formData.append('role', data.role || '')
        formData.append('linkedinUrl', data.linkedinUrl || '')
        formData.append('twitterUrl', data.twitterUrl || '')
        formData.append('telegramUrl', data.telegramUrl || '')
        formData.append('departmentId', data.departmentId)
        formData.append('managerId', data.managerId === "none" ? '' : (data.managerId || ''))
        
        // Set default compensation values (hidden from users)
        formData.append('compensation', 'monthly')
        
        formData.append('active', data.active.toString())

        const result = await saveEmployeeAction(formData)

        if (result.success) {
          console.log('✅ [ADD EMPLOYEE] Employee created successfully')

          // If we have selected managers, assign them
          if (selectedManagers.length > 0) {
            console.log('🔄 [ADD EMPLOYEE] Assigning managers...')
            
            // Wait a moment for the employee to be created, then find by email
            await new Promise(resolve => setTimeout(resolve, 500))
            
            try {
              const { getEmployees } = await import('@/lib/data/employees')
              const employees = await getEmployees()
              const newEmployee = employees.find(emp => emp.email === data.email)
              
              if (newEmployee?.id) {
                const managerIds = selectedManagers.map(m => m.id)
                const primaryManagerId = selectedManagers.find(m => m.isPrimary)?.id

                console.log('🔄 [ADD EMPLOYEE] Assigning managers:', { managerIds, primaryManagerId })

                const managerResult = await assignManagersToEmployeeAction(
                  newEmployee.id,
                  managerIds,
                  primaryManagerId
                )

                if (!managerResult.success) {
                  console.warn('⚠️ [ADD EMPLOYEE] Failed to assign managers:', managerResult.error)
                  // Don't fail the whole operation, just warn
                } else {
                  console.log('✅ [ADD EMPLOYEE] Managers assigned successfully')
                }
              } else {
                console.warn('⚠️ [ADD EMPLOYEE] Could not find newly created employee for manager assignment')
              }
            } catch (error) {
              console.warn('⚠️ [ADD EMPLOYEE] Error during manager assignment:', error)
            }
          }

          setIsSuccess(true)
          const managerCount = selectedManagers.length
          const managerText = managerCount > 0 ? ` with ${managerCount} manager(s)` : ''

          toast.success('Employee added successfully!', {
            description: `${data.fullName} has been added to the system${managerText}.`
          })

          // Redirect after a brief delay
          setTimeout(() => {
            router.push('/dashboard/employees')
          }, 2000)
        } else {
          setSubmitError('error' in result ? result.error : 'Failed to save employee')
          toast.error('Failed to save employee', {
            description: 'error' in result ? result.error : 'An error occurred while saving the employee.'
          })
        }
      } catch (error) {
        console.error('❌ [ADD EMPLOYEE] Form submission error:', error)
        setSubmitError('An unexpected error occurred. Please try again.')
        toast.error('Unexpected error', {
          description: 'An unexpected error occurred. Please try again.'
        })
      }
    })
  }

  return (
    <div className="space-y-6">
      {/* Header with back button */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
          disabled={isPending}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
      </div>

      {submitError && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Enter the employee's basic details and contact information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="fullName">Full Name *</Label>
                <Input
                  id="fullName"
                  {...form.register('fullName')}
                  placeholder="Enter full name"
                  disabled={isPending}
                />
                {form.formState.errors.fullName && (
                  <p className="text-sm text-destructive">{form.formState.errors.fullName.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  {...form.register('email')}
                  placeholder="Enter email address"
                  disabled={isPending}
                />
                {form.formState.errors.email && (
                  <p className="text-sm text-destructive">{form.formState.errors.email.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="role">Role</Label>
              <Input
                id="role"
                {...form.register('role')}
                placeholder="e.g., Software Engineer, Marketing Manager"
                disabled={isPending}
              />
            </div>
          </CardContent>
        </Card>

        {/* Department */}
        <Card>
          <CardHeader>
            <CardTitle>Department</CardTitle>
            <CardDescription>
              Assign the employee to a department
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="departmentId">Department *</Label>
              <Select
                value={form.watch('departmentId')}
                onValueChange={(value) => form.setValue('departmentId', value)}
                disabled={isPending}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a department" />
                </SelectTrigger>
                <SelectContent>
                  {departments.map((dept) => (
                    <SelectItem key={dept.id} value={dept.id}>
                      {dept.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.departmentId && (
                <p className="text-sm text-destructive">{form.formState.errors.departmentId.message}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Manager Assignment */}
        <SimpleManagerSelector
          managers={managers}
          selectedManagers={selectedManagers}
          onManagersChange={setSelectedManagers}
          disabled={isPending}
        />

        {/* Social Links */}
        <Card>
          <CardHeader>
            <CardTitle>Social Links (Optional)</CardTitle>
            <CardDescription>
              Add social media profiles for the employee
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="linkedinUrl">LinkedIn URL</Label>
                <Input
                  id="linkedinUrl"
                  {...form.register('linkedinUrl')}
                  placeholder="https://linkedin.com/in/..."
                  disabled={isPending}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="twitterUrl">Twitter URL</Label>
                <Input
                  id="twitterUrl"
                  {...form.register('twitterUrl')}
                  placeholder="https://twitter.com/..."
                  disabled={isPending}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="telegramUrl">Telegram URL</Label>
                <Input
                  id="telegramUrl"
                  {...form.register('telegramUrl')}
                  placeholder="https://t.me/..."
                  disabled={isPending}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Status */}
        <Card>
          <CardHeader>
            <CardTitle>Status</CardTitle>
            <CardDescription>
              Set the employee's active status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Switch
                id="active"
                checked={form.watch('active')}
                onCheckedChange={(checked) => form.setValue('active', checked)}
                disabled={isPending}
              />
              <Label htmlFor="active">Active Employee</Label>
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end gap-4 pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isPending}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isPending || isSuccess}
            className={isSuccess ? "bg-green-600 hover:bg-green-600" : ""}
          >
            {isSuccess ? (
              <>
                <Check className="mr-2 h-4 w-4" />
                Success!
              </>
            ) : isPending ? (
              <>
                <Loader className="mr-2 h-4 w-4 animate-spin" />
                Creating Employee...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Create Employee
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
